import os
import logging
from typing import List, Dict, Any
import time
import asyncio
import uuid
import urllib.parse
import aiohttp
import spotipy
import requests

from cachetools import TTL<PERSON>ache
from dotenv import load_dotenv
from aiohttp import web
from spotipy.oauth2 import SpotifyOAuth
from telegram import InlineKeyboardButton, InlineKeyboardMarkup, InlineQueryResultsButton, Update, InlineQueryResultAudio, InlineQueryResultArticle, InputTextMessageContent
from telegram.ext import (
    Application,
    CommandHandler,
    InlineQueryHandler,
    ChosenInlineResultHandler,
    ContextTypes
)

# Настройка логирования
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

# Загрузка переменных окружения
load_dotenv()
TELEGRAM_TOKEN = os.getenv('TELEGRAM_TOKEN')

async def get_full_audio_url(song_name: str, artist_name: str, spotify_url: str) -> str:
    """Get the full audio URL using the spotisongdownloader.to API."""
    api_url = 'https://spotisongdownloader.to/api/composer/spotify/wertyuht3456.php'
    headers = {
        'accept-language': 'en-US,en;q=0.9',
        'content-type': 'application/x-www-form-urlencoded; charset=UTF-8',
        'origin': 'https://spotisongdownloader.to',
        'referer': 'https://spotisongdownloader.to/track.php'
    }
    data = {
        'song_name': song_name,
        'artist_name': artist_name,
        'url': spotify_url
    }

    try:
        async with aiohttp.ClientSession() as session:

            async with session.post('https://spotisongdownloader.to/ident') as response:
                logger.info(f"Get session from spotisongdownloader status: {response.status}")
                php_sessid = response.cookies.get('PHPSESSID')
                if php_sessid:
                    headers['cookie'] = f'PHPSESSID={php_sessid}; quality=m4a; dcount=0'
                else:
                    headers['cookie'] = f'PHPSESSID=stht03in7lvnmdmumc3gafphlh; quality=m4a; dcount=0'
                    logger.info("Use default PHPSESSID value")
                await response.release()

            async with session.get(f'https://spotisongdownloader.to/api/composer/spotify/xsingle_track.php?url={spotify_url}') as response:
                logger.info(f"Bump track for download. Status code: {response.status}")
                await response.release()

            async with session.post(api_url, headers=headers, data=data) as response:
                logger.info(f"Response status: {response.status}")
                content_type = response.headers.get('Content-Type', '')
                logger.info(f"Response Content-Type: {content_type}")
                
                if response.status != 200:
                    logger.error(f"HTTP error: status {response.status}")
                    response_text = await response.text()
                    logger.error(f"Response text: {response_text}")
                    raise ValueError(f"HTTP error: status {response.status}")
                
                json_response = await response.json()
                dlink = json_response.get('dlink')
                if not dlink:
                    logger.error("Field 'dlink' is missing in the API response")
                    raise ValueError("Field 'dlink' is missing in the API response")
                dlink = urllib.parse.quote(dlink, safe=':/?=&')
                return dlink
    except aiohttp.ClientError as e:
        logger.error(f"Connection error: {e}")
        return None
    except ValueError as e:
        logger.error(f"Value error: {e}")
        return None
    except Exception as e:
        logger.error(f"Unknown error: {e}")
        return None
    
async def start_command(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """Обработчик команды /start."""
    user = update.effective_user
    await update.message.reply_text(f'Привет, {user.first_name}! Я бот для демонстрации работы с Telegram API.')

async def help_command(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """Обработчик команды /help."""
    await update.message.reply_text('Доступные команды:\n/start - Начать взаимодействие\n/help - Показать эту справку')
    
    await context.bot.send_audio(
        chat_id=update.effective_chat.id,
        audio='https://nowplaybot.sijykijy.com:8443/a2/a.mp3'
    )

# async def main():
    # async with aiohttp.ClientSession() as session:
    #     test = await get_full_audio_url('Aboba click', 'Aboba', 'https://open.spotify.com/track/09By4L1F4e7kcAtLIvsRuJ')

def main():
    """Основная функция для запуска бота."""
    logger.info("Запуск бота...")

    from telegram.request import HTTPXRequest
    request = HTTPXRequest(
        http_version="1.1",
        connection_pool_size=8,
        read_timeout=5.0,
        write_timeout=5.0,
        connect_timeout=5.0,
        httpx_kwargs={
            "verify": False,
            "trust_env": False
        }
    )

    # Инициализация бота
    application = Application.builder().token(TELEGRAM_TOKEN).request(request).get_updates_request(request).build()

    # Добавляем обработчики команд
    application.add_handler(CommandHandler("start", start_command))
    application.add_handler(CommandHandler("help", help_command))
    
    # Запуск поллинга (бесконечный цикл получения обновлений)
    logger.info("Бот запущен. Начинаем поллинг...")
    application.run_polling(allowed_updates=Update.ALL_TYPES)

if __name__ == '__main__':
    # asyncio.run(main())
    main()