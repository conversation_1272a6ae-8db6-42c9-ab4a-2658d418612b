# Use the specified Python runtime
FROM python:3.9.7-slim

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONUNBUFFERED=1

# Set working directory
WORKDIR /app

# Install dependencies
COPY requirements.txt .
RUN pip install --upgrade pip && pip install -r requirements.txt

# Copy py-files
COPY main.py .
COPY spotiSongDownloader.py .
COPY notifier.py .
COPY getTracks.py .

# Expose the port
# EXPOSE 8000

# Run the application
CMD ["python", "main.py"]