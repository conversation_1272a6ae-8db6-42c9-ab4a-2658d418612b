import os
import logging
from typing import List, Dict, Any
import time
import asyncio
import uuid
import urllib.parse
import aiohttp
import spotipy
import mimetypes
import datetime
import hashlib

from cachetools import TTLCache
from dotenv import load_dotenv
from aiohttp import web
from spotipy.oauth2 import SpotifyOAuth
from telegram import InlineKeyboardButton, InlineKeyboardMarkup, InlineQueryResultsButton, Update, InlineQueryResultAudio, InlineQueryResultArticle, InputTextMessageContent, InlineQueryResultCachedAudio
from telegram.ext import (
    Application,
    CommandHandler,
    InlineQueryHandler,
    ChosenInlineResultHandler
)
from spotiSongDownloader import download_track, get_api
from notifier import set_notify_owner_function, set_notify_user_function

# Настройка логирования
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

# Загрузка переменных окружения
load_dotenv()

try:
    TELEGRAM_TOKEN = os.getenv('TELEGRAM_TOKEN')
    SPOTIFY_CLIENT_ID = os.getenv('SPOTIFY_CLIENT_ID')
    SPOTIFY_CLIENT_SECRET = os.getenv('SPOTIFY_CLIENT_SECRET')
    SPOTIFY_REDIRECT_URI = os.getenv('SPOTIFY_REDIRECT_URI')
    WEB_SERVER_HOST = os.getenv('WEB_SERVER_HOST', '0.0.0.0')
    WEB_SERVER_PORT = int(os.getenv('PORT', default=8443))
    APP_URL = os.environ["APP_URL"]
    TELEGRAM_OWNER_ID = int(os.getenv('TELEGRAM_OWNER_ID', default=246259999))

    if not all([TELEGRAM_TOKEN, SPOTIFY_CLIENT_ID, SPOTIFY_CLIENT_SECRET, SPOTIFY_REDIRECT_URI, APP_URL, TELEGRAM_OWNER_ID]):
        raise KeyError("One or more environment variables are missing")
except KeyError:
    logger.error('Error when getting variables')
    raise

# Инициализация Spotify клиента
sp_oauth = SpotifyOAuth(
    client_id=SPOTIFY_CLIENT_ID,
    client_secret=SPOTIFY_CLIENT_SECRET,
    redirect_uri=SPOTIFY_REDIRECT_URI,
    scope="user-read-currently-playing user-read-recently-played"
)

# webhook settings
WEBHOOK_HOST = APP_URL
WEBHOOK_PATH = f"/webhook/{TELEGRAM_TOKEN}"
WEBHOOK_URL = f"{WEBHOOK_HOST}{WEBHOOK_PATH}"

# Словарь для хранения токенов пользователей
user_tokens = {}
# Кэш для хранения ссылок на один день (24 часа * 60 минут * 60 секунд = 86400 секунд)
song_link_cache = TTLCache(maxsize=1000, ttl=24 * 60 * 60)
start_time = time.time()

async def refresh_spotify_tokens():
    """Функция для обновления токенов"""
    logger.info("Starting token refresh task")
    while True:
        try:
            for user_id, token_info in user_tokens.items():
                expires_at = token_info['expires_at']
                current_time = int(time.time())
                if expires_at - current_time < 60 * 6:  # Обновляем токен за 6 минут до истечения
                    new_token_info = sp_oauth.refresh_access_token(token_info['refresh_token'])
                    user_tokens[user_id] = new_token_info
                    logger.info(f"Token for user {user_id} refreshed successfully. Next refresh '{token_info['expires_at']}'")
        except Exception as e:
            logger.error(f"Error refreshing tokens: {str(e)}")

        await asyncio.sleep(60 * 3)  # Проверяем токены каждые 3 минуты

async def start_background_tasks(app):
    """Запуск обновления токенов в фоновом режим"""
    asyncio.create_task(refresh_spotify_tokens())

async def notify_owner(message: str, disable_notification: bool = False):
    """Отправка сообщения владельцу бота."""
    await application.bot.send_message(chat_id=TELEGRAM_OWNER_ID, text=f"[O-🔔] {message}", disable_notification=disable_notification)

async def notify_user(user_id: int, message: str):
    """Отправка сообщения пользователю бота."""
    try:
        await application.bot.send_message(chat_id=user_id, text=f"[🔔] {message}")
        logger.info(f"Notification sent to user {user_id}: {message}")
    except Exception as e:
        logger.error(f"Failed to send notification to user {user_id}: {str(e)}")
        await notify_owner(f"Failed to send notification to user {user_id}: {str(e)}")

async def get_current_and_recent_tracks(access_token: str) -> List[Dict[str, Any]]:
    """Получение текущего трека и двух последних прослушанных треков."""
    logger.info("Getting current and recent tracks")
    sp = spotipy.Spotify(auth=access_token)

    current_track = sp.current_user_playing_track()
    #recent_tracks = sp.current_user_recently_played(limit=2)

    tracks = []

    if current_track and current_track['item']:
        tracks.append({
            'id': current_track['item']['id'],
            'name': current_track['item']['name'],
            'artist': current_track['item']['artists'][0]['name'],
            'album': current_track['item']['album']['name'],
            'preview_url': current_track['item']['preview_url'],
            'album_cover_url': current_track['item']['album']['images'][0]['url']  # URL обложки альбома
        })

    # for item in recent_tracks['items']:
    #     track = item['track']
    #     tracks.append({
    #         'id': track['id'],
    #         'name': track['name'],
    #         'artist': track['artists'][0]['name'],
    #         'album': track['album']['name'],
    #         'preview_url': track['preview_url'],
    #         'album_cover_url': track['album']['images'][0]['url']  # URL обложки альбома
    #     })

    return tracks
    #return tracks[:3]  # Возвращаем максимум 3 трека

async def get_current_track(access_token: str):
    """Get the last played track using Spotify API."""
    logger.info("Getting the last played track")
    sp = spotipy.Spotify(auth=access_token)

    current_track = sp.current_user_playing_track()
    if current_track and current_track['item']:
        return {
            'id': current_track['item']['id'],
            'name': current_track['item']['name'],
            'artist': current_track['item']['artists'][0]['name'],
            'album': current_track['item']['album']['name'],
            'preview_url': current_track['item']['preview_url'],
            'album_cover_url': current_track['item']['album']['images'][0]['url'],  # URL обложки альбома
            'spotify_url': current_track['item']['external_urls']['spotify']
        }
    return None

async def get_song_link(track_id: str) -> str:
    """Получение универсальной ссылки на трек через song.link API."""
    if track_id in song_link_cache:
        return song_link_cache[track_id]

    return f"https://song.link/s/{track_id}"
    # https://api.song.link/v1-alpha.1/links?key=71d7be8a-3a76-459b-b21e-8f0350374984&url=spotify:track:2CBfrFdg6JT6PadB3DK213
    # Посмотреть быстрее ли с ключиком
    # logger.info(f"Getting song link for track with id '{track_id}'")
    # async with aiohttp.ClientSession() as session:
    #     async with session.get(f"https://api.song.link/v1-alpha.1/links?url=spotify:track:{track_id}") as response:
    #         data = await response.json()
    #         song_link_cache[track_id] = data['pageUrl']
    #         return data['pageUrl']

async def get_full_audio_url(song_name: str, artist_name: str, album: str, spotify_url: str, user_id: int = None) -> str:
    """Get the full audio URL using the spotisongdownloader module."""
    logger.info(f"[GetFullAudioUrl] Getting full audio URL for {song_name} by {artist_name}")

    track_id = spotify_url.split('/')[-1]
    if '?' in track_id:
        track_id = track_id.split('?')[0]

    logger.info(f"[GetFullAudioUrl] Extracted track ID: {track_id}")

    try:
        track_metadata = {
            'song_name': song_name,
            'artist': artist_name,
            'url': spotify_url,
            'album': album,
        }
        download_link = download_track(track_id, user_id, track_metadata)

        if download_link:
            logger.info(f"[GetFullAudioUrl] Successfully retrieved download link for '{song_name}'. Link: {download_link}")

            # # Проксируем через наш сервер с корректным content-type
            # prefix = "https://spotisongdownloader.to/api/composer/ffmpeg/saved/"
            # if download_link.startswith(prefix):
            #     file_name = download_link[len(prefix):]
            #     proxied_url = f"{APP_URL}/a/{file_name}"
            #     logger.info(f"[GetFullAudioUrl] Proxying through our server: {proxied_url}")
            #     return proxied_url
            return download_link
        else:
            logger.error(f"[GetFullAudioUrl] Failed to get download link for '{song_name}'")
            return None
    except Exception as e:
        logger.error(f"[GetFullAudioUrl] Error getting audio URL: {str(e)}")
        return None

async def start(update: Update, context: Any) -> None:
    """Обработчик команды /start."""
    user_id = update.effective_user.id
    auth_url = sp_oauth.get_authorize_url(state=str(user_id))
    keyboard = [[InlineKeyboardButton(text="Abibus", url=auth_url)]]
    reply_markup = InlineKeyboardMarkup(keyboard)
    await notify_owner(f"User '{update.effective_user.name}' (Id: {user_id}) started the bot")
    await update.message.reply_text(
        "Hii!",
        reply_markup=reply_markup
    )

async def change_url(update: Update, context: Any) -> None:
    """Обработчик команды /change {URL} для изменения URL сервиса скачивания музыки."""
    user_id = update.effective_user.id

    # Проверяем, является ли пользователь владельцем бота
    if user_id != TELEGRAM_OWNER_ID:
        await update.message.reply_text("))")
        await notify_owner(f"⚠️ Пользователь {update.effective_user.name} (ID: {user_id}) попытался использовать команду /change")
        return

    if not context.args or len(context.args) < 1:
        await update.message.reply_text("❌ Пожалуйста, укажите URL. Пример: /change https://awd13.mymp3.xyz")
        return
    new_url = context.args[0]

    if not (new_url.startswith("http://") or new_url.startswith("https://")):
        await update.message.reply_text("❌ URL должен начинаться с http:// или https://")
        return

    from spotiSongDownloader import update_music_service_url
    old_url = update_music_service_url(new_url)

    await update.message.reply_text(f"✅ URL сервиса скачивания музыки успешно изменен.\nСтарый URL: {old_url}\nНовый URL: {new_url}")

async def inline_query(update: Update, context: Any) -> None:
    """Обработчик inline-запросов."""
    query = update.inline_query.query
    user_id = update.effective_user.id
    token_info = user_tokens.get(user_id)

    logger.info(f"User {user_id} made an inline query: {query}")

    try:
        if not token_info:
            logger.info(f"User {user_id} is not authorized")
            await update.inline_query.answer(
            results= [
                # InlineQueryResultArticle(
                #     id=f"auth_required-{str(uuid.uuid4())}",
                #     title="Требуется авторизация",
                #     input_message_content=InputTextMessageContent('Вы выбрали этот результат!'),
                #     description="Нажмите здесь, чтобы начать авторизацию",
                # )
            ],
            button=InlineQueryResultsButton(
                text="Authorize",
                start_parameter="login"
            ),
            cache_time=0
            )
            return

        if token_info['expires_at'] <= int(time.time()):
            logger.info(f"Refreshing access token for user {user_id}")
            token_info = sp_oauth.refresh_access_token(token_info['refresh_token'])
            user_tokens[user_id] = token_info

        results = []

        # Получаем треки даже если запрос пустой
        current_track = await get_current_track(token_info['access_token'])

        # Если результатов нет, добавляем сообщение об этом
        if current_track is None:
            results.append(
                InlineQueryResultArticle(
                    id=f"no_tracks-{user_id}-{int(time.time())}",
                    title="No tracks found",
                    input_message_content=InputTextMessageContent("No tracks found in spotify")
                )
            )
        else:
            current_track['song_link'] = await get_song_link(current_track['id'])

            if current_track['preview_url'] is None:
                logger.info("Preview URL is missing. Trying to get full audio URL")
                full_audio_url = await get_full_audio_url(current_track['name'], current_track['artist'], current_track['album'], current_track['spotify_url'], user_id)
                current_track['preview_url'] = full_audio_url

            logger.info(f"Found track: {current_track['name']} by {current_track['artist']}. Id: '{current_track['id']}' Preview URL: {current_track['preview_url']}. Song link: {current_track['song_link']}")
            if current_track['preview_url']:
                results.append(
                    InlineQueryResultAudio(
                        id=f"tracks-{current_track['id']}-{user_id}-{str(uuid.uuid4()).replace('-', '')[:16]}",
                        audio_url=current_track['preview_url'],
                        title=current_track['name'],
                        performer=current_track['artist'],
                        caption=f"<b><a href=\"{current_track['song_link']}\">📝 Song-link</a></b>\n--- --- ---\n<b>{current_track['artist']}</b> - <b>{current_track['name']}</b>\n<i>Album</i>: {current_track['album']}",
                        parse_mode='HTML',
                        # thumb_url=track.get('album_image_url', 'https://cdn-icons-png.flaticon.com/512/1834/1834342.png')  # Add this line
                    )
                )
            else:
                results.append(
                    InlineQueryResultArticle(
                        id=f"tracks-{current_track['id']}-{user_id}-{str(uuid.uuid4()).replace('-', '')[:16]}",
                        title=f"{current_track['name']} (No audio or processing)",
                        input_message_content=InputTextMessageContent(
                            f"<b><a href=\"{current_track['song_link']}\">📝 Song-link</a></b>\n--- --- ---\n<b>{current_track['artist']}</b> - <b>{current_track['name']}</b>\n<i>Album</i>: {current_track['album']}",
                            parse_mode='HTML'
                        ),
                        description=f"{current_track['artist']} - {current_track['name']}"
                    )
                )

        await update.inline_query.answer(results, cache_time=0)
    except Exception as e:
        logger.error(f"Error answering inline query: {str(e)}")
        await update.inline_query.answer(
            results=[
                InlineQueryResultArticle(
                    id=f"error-{user_id}-{int(time.time())}",
                    title="Error :(",
                    input_message_content=InputTextMessageContent("An error occurred while processing your request. Please try again.")
                )
            ],
            cache_time=0
        )

async def chosen_result(update: Update, context: Any) -> None:
    """Обработчик выбранного результата."""
    result = update.chosen_inline_result
    track_id = result.result_id

    return

    try:
        logger.info(f"User {update.effective_user.id} chose the result: {track_id}")
        # Получаем ссылку на скачивание с spotifydown.com
        download_url = await get_spotifydown_url(track_id)
        if not download_url:
            raise ValueError("Не удалось получить ссылку на скачивание трека.")

        # Получаем универсальную ссылку на трек
        song_link = await get_song_link(track_id)

        # Обновляем inline-сообщение с новой информацией
        await context.bot.edit_message_text(
            inline_message_id=result.inline_message_id,
            text=f"{result.query}\n[Слушать]({download_url})\n[song.link]({song_link})",
            parse_mode='Markdown'
        )

    except Exception as e:
        logger.error(f"Ошибка при обработке выбранного результата: {str(e)}")
        # Обновляем inline-сообщение с информацией об ошибке
        await context.bot.edit_message_text(
            inline_message_id=result.inline_message_id,
            text=f"An error occurred while downloading the track: {str(e)}"
        )

async def spotify_callback(request):
    """Обработчик callback от Spotify."""
    code = request.query.get('code')
    state = request.query.get('state')

    if code:
        token_info = sp_oauth.get_access_token(code)
        user_id = int(state)
        user_tokens[user_id] = token_info
        logger.info(f"User {user_id} successfully authorized. Token info: {token_info}")
        await application.bot.send_message(chat_id=user_id, text="Authorization successful! You can now use the bot.")
        await notify_owner(f"User {user_id} successfully authorized")
        bot_username = (await application.bot.get_me()).username
        if not bot_username:
            return web.Response(text="Authorization successful! You can return to Telegram.")
        return web.HTTPFound(f"https://t.me/{bot_username}")
    else:
        await application.bot.send_message(chat_id=user_id, text="Authorization error! 🥹")
        bot_username = (await application.bot.get_me()).username
        if not bot_username:
            return web.Response(text="Authorization error.", status=400)
        return web.HTTPFound(f"https://t.me/{bot_username}")

async def proxy_audio_file(request):
    """Проксируем файл через наш сервер."""
    file_name = request.match_info.get('file_name')
    if not file_name:
        return web.Response(text="File name is required", status=400)
    
    file_url = f"https://sijykijy.com/img/{file_name}"
    try:
        _, ext = os.path.splitext(file_name)
        content_type = None

        if ext.lower() == '.mp3':
            content_type = 'audio/mpeg'
        elif ext.lower() == '.m4a':
            content_type = 'audio/x-m4a'
        elif ext.lower() == '.wav':
            content_type = 'audio/wav'
        elif ext.lower() == '.ogg':
            content_type = 'audio/ogg'
        elif ext.lower() == '.flac':
            content_type = 'audio/flac'
        else:
            # Пытаемся определить тип по расширению
            content_type, _ = mimetypes.guess_type(file_name)
            if not content_type:
                content_type = 'application/octet-stream'

        # Создаем клиентскую сессию для запроса к удаленному серверу
        async with aiohttp.ClientSession() as session:
            # Добавляем заголовки для имитации обычного браузера
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                'Referer': 'https://awd1.mymp3.xyz/phmp3?fname=One%2520Inch%2520Punch-Yin%2520Yin.m4a', # Ставить динамически
                'Host': 'awd1.mymp3.xyz', # Тоже ставить динамечески
                #'Origin': 'https://spotisongdownloader.to'
            }

            # Выполняем GET-запрос к удаленному серверу
            async with session.get(file_url, headers=headers) as response:
                if response.status != 200:
                    return web.Response(text=f"Failed to fetch file: {response.status}", status=response.status)

                # Получаем текущее время для заголовка last-modified
                current_time = datetime.datetime.now(datetime.timezone.utc).strftime('%a, %d %b %Y %H:%M:%S GMT')

                # Генерируем ETag на основе имени файла и текущего времени
                etag_value = f'"{hashlib.md5(f"{file_name}_{current_time}".encode()).hexdigest()}"'

                # Создаем StreamResponse для потоковой передачи файла
                resp = web.StreamResponse(
                    status=200,
                    headers={
                        'Content-Type': content_type,
                        'Content-Disposition': f'attachment; filename="{file_name}"',
                        'Accept-Ranges': 'bytes',  # Важный заголовок для Telegram
                        'Cache-Control': 'max-age=14400',  # Кэширование на 4 часа
                        'last-modified': current_time,
                        'etag': etag_value
                    }
                )

                # Если известен размер файла, добавляем его в заголовок
                if 'Content-Length' in response.headers:
                    resp.headers['Content-Length'] = response.headers['Content-Length']

                # Начинаем отправку ответа
                await resp.prepare(request)

                # Передаем данные потоково
                async for chunk in response.content.iter_chunked(8192):  # 8KB chunks
                    await resp.write(chunk)

                await resp.write_eof()
                return resp

    except Exception as e:
        logging.error(f"Error proxying audio file: {str(e)}")
        return web.Response(text=f"Error processing request: {str(e)}", status=500)

    except Exception as e:
        return web.Response(text=f"Error: {str(e)}", status=500)

async def webhook_handler(request):
    try:
        update = Update.de_json(await request.json(), application.bot)
        await application.process_update(update)
        return web.Response()
    except Exception as e:
        logger.error(f"Error processing update: {str(e)}")
        return web.Response(status=500)

async def index_handler(request):
    current_time = time.time()
    uptime_seconds = int(current_time - start_time)
    uptime_str = time.strftime("%H:%M:%S", time.gmtime(uptime_seconds))
    return web.Response(text=f"Hii!👋\nUptime: {uptime_str}", content_type='text/html')

async def run_web_server():
    """Запуск веб-сервера."""
    app = web.Application()
    app.router.add_get('/', index_handler)
    app.router.add_get('/spotify-callback', spotify_callback)
    app.router.add_post(WEBHOOK_PATH, webhook_handler)
    app.router.add_get('/a/{fileId}', proxy_audio_file)

    app.on_startup.append(start_background_tasks) # Фоновая задача по обновлению токенов

    runner = web.AppRunner(app)
    await runner.setup()
    site = web.TCPSite(runner, WEB_SERVER_HOST, WEB_SERVER_PORT)
    await site.start()
    logger.info(f"Web server started at http://{WEB_SERVER_HOST}:{WEB_SERVER_PORT}")

async def run_telegram_bot():
    global application
    """Запуск Telegram бота."""
    logger.info("Начало инициализации Telegram бота")
    application = Application.builder().token(TELEGRAM_TOKEN).build()
    application.add_handler(CommandHandler("start", start))
    application.add_handler(CommandHandler("change", change_url))
    application.add_handler(InlineQueryHandler(inline_query))
    application.add_handler(ChosenInlineResultHandler(chosen_result))

    logger.info("Инициализация Telegram бота")
    await application.initialize()
    logger.info("Запуск Telegram бота")
    await application.start()
    #logger.info("Начало поллинга Telegram бота")
    #await application.updater.start_polling(allowed_updates=Update.ALL_TYPES)
    logger.info(f"Начало webhook Telegram бота. URL: {WEBHOOK_URL}")
    await application.bot.set_webhook(url=WEBHOOK_URL)

    set_notify_owner_function(notify_owner, asyncio.get_event_loop())
    set_notify_user_function(notify_user)

    logger.info("Telegram бот успешно запущен и работает")

    # Бесконечный цикл для поддержания работы бота
    while True:
        await asyncio.sleep(1)  # Ожидание 1 секунду между проверками
        if not application.running:
            break

    logger.info("Остановка Telegram бота")
    await application.stop()

async def main():
    """Основная функция для запуска бота и веб-сервера."""
    web_server_task = asyncio.create_task(run_web_server())
    telegram_bot_task = asyncio.create_task(run_telegram_bot())

    try:
        # Ожидаем выполнения обеих задач
        await asyncio.gather(web_server_task, telegram_bot_task)
    except asyncio.CancelledError:
        logger.info("Завершение работы...")
    except Exception as e:
        logger.error(f"Произошла ошибка: {str(e)}")
        with open("crash.log", "a") as crash_log:
            crash_log.write(f"{time.strftime('%Y-%m-%d %H:%M:%S')} - Произошла ошибка: {str(e)}\n")
    finally:
        # Отменяем задачи при выходе
        web_server_task.cancel()
        telegram_bot_task.cancel()
        # Ждем, пока задачи завершатся
        await asyncio.gather(web_server_task, telegram_bot_task, return_exceptions=True)
        logger.info("Бот остановлен")

if __name__ == '__main__':
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("Программа остановлена пользователем")
    except Exception as e:
        logger.error(f"Произошла ошибка: {str(e)}")
        with open("crash.log", "a") as crash_log:
            crash_log.write(f"{time.strftime('%Y-%m-%d %H:%M:%S')} - Произошла ошибка: {str(e)}\n")
