import logging
import requests
import re
import urllib.parse
import json

# Thx https://github.com/afkarxyz/SpotiSongDownloader/blob/6517e67f8ce36f876bf609b85625feee194478b2/getTracks.py

class SpotiSongDownloader:
    logging.basicConfig(
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        level=logging.INFO
    )
    logger = logging.getLogger(__name__)

    def __init__(self):
        self.cookies = {}
        self.user_agent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
        self.api_url = None
        self.base_url = "https://spotisongdownloader.to/"

    def get_headers(self, referer=None, with_cookies=False, is_post=False):
        headers = {
            "accept": "application/json, text/javascript, */*; q=0.01",
            "accept-language": "en-US,en;q=0.9",
            "user-agent": self.user_agent,
            "referer": referer or self.base_url,
            "x-requested-with": "XMLHttpRequest"
        }
        
        if with_cookies:
            headers["cookie"] = '; '.join([f"{k}={v}" for k, v in self.cookies.items()])
            
        if is_post:
            headers.update({
                "content-type": "application/x-www-form-urlencoded; charset=UTF-8",
                "origin": self.base_url.rstrip('/')
            })
            
        self.logger.info(f"[SpotiSongDownloader.get_headers] Headers: {headers}")
        return headers

    def prepare_auth(self):
        try:
            url = f"{self.base_url}createToken.php"
            res = requests.get(url, headers=self.get_headers())
            token_match = re.search(r'cf_token=([^;]+)', res.text)
            if token_match and token_match.group(1):
                self.cookies['cf_token'] = token_match.group(1)
        except Exception:
            pass
            
        if not self.api_url:
            try:
                url = f"{self.base_url}track.php"
                res = requests.get(url, headers=self.get_headers())
                match = re.search(r'url:\s*"(\/api\/composer\/spotify\/[^"]+)"', res.text)
                if match and match.group(1):
                    self.api_url = f"{self.base_url}{match.group(1).lstrip('/')}"
            except Exception:
                pass
                
        return bool(self.cookies.get('cf_token') and self.api_url)
    
    def prepare_auth_without_cftoken(self):
        try:
            res = requests.get('https://spotisongdownloader.to/', headers=self.get_headers())
            self.cookies = {
                'PHPSESSID': res.cookies.get('PHPSESSID'),
                'quality': 'm4a'
            }
        except Exception as e:
            self.logger.error(f"[SpotiSongDownloader.prepare_auth_without_cftoken] Error getting PHPSESSID: {str(e)}")
            pass

        if not self.api_url:
            try:
                url = f"{self.base_url}track.php"
                res = requests.get(url, headers=self.get_headers())
                match = re.search(r'url:\s*"(\/api\/composer\/spotify\/[^"]+)"', res.text)
                if match and match.group(1):
                    self.api_url = f"{self.base_url}{match.group(1).lstrip('/')}"
            except Exception as e:
                self.logger.error(f"[SpotiSongDownloader.prepare_auth_without_cftoken] Error getting API URL: {str(e)}")
                pass
                
        return bool(self.api_url)    

    def clean_text(self, text):
        if text is None:
            return ""
        return re.sub(r'[^\w\s-]', '', text.replace('&amp;', '&')).strip()

    def get_track_info(self, url):
        api_url = f"{self.base_url}api/composer/spotify/xsingle_track.php"
        res = requests.get(
            api_url, 
            headers=self.get_headers(f"{self.base_url}track.php", with_cookies=True), 
            params={"url": url}
        )

        try:
            data = res.json()
            if data.get('res') != 200:
                return None

            return {
                "song_name": self.clean_text(data.get('song_name')),
                "artist": self.clean_text(data.get('artist')),
                "img": data.get('img'),
                "duration": data.get('duration'),
                "url": data.get('url'),
                "released": data.get('released'),
                "album": data.get('album_name')
            }
        except Exception as e:
            self.logger.error(f"[SpotiSongDownloader.get_track_info] Error parsing JSON: {str(e)} | Response: {res.text}")
            return None

    def get_download_link(self, track_info):
        if not track_info or not track_info.get('song_name') or not track_info.get('artist') or not self.api_url:
            self.logger.error("[SpotiSongDownloader.get_download_link] Invalid track info or API URL")
            return None
        
        form_data = {
            "song_name": track_info.get('song_name'),
            "artist_name": track_info.get('artist'),
            "url": track_info.get('url', "")
        }
        
        form = urllib.parse.urlencode(form_data)

        self.logger.info(f"[SpotiSongDownloader.get_download_link] Getting download link. Link: {self.api_url}. Data: {form_data}")
        res = requests.post(
            self.api_url, 
            data=form, 
            headers=self.get_headers(f"{self.base_url}track.php", with_cookies=True, is_post=True)
        )

        try:
            data = res.json()
            if data.get('status') == "success" and data.get('dlink'):
                return {"dlink": data.get('dlink').replace('\/', '/')}
            return data
        except Exception as e:
            self.logger.error(f"[SpotiSongDownloader.get_download_link] Error parsing JSON: {str(e)} | Response: {res.text}")
            return None

    def get_download_info(self, url):
        self.cookies = {}
        self.api_url = None
        
        self.logger.info(f"[SpotiSongDownloader.get_download_info] Getting download info for: {url}")
        # if not self.prepare_auth():
        #     return {"error": "Failed to initialize authentication"}

        if not self.prepare_auth_without_cftoken():
            return {"error": "Failed to initialize authentication"}

        track_info = self.get_track_info(url)
        self.logger.info(f"[SpotiSongDownloader.get_download_info] Track info: {track_info}")
        if not track_info:
            return {"error": "Track not found"}

        download_data = self.get_download_link(track_info)
        self.logger.info(f"[SpotiSongDownloader.get_download_info] Download data: {download_data}")
        if not download_data:
            return {"error": "Failed to fetch download link"}

        if download_data.get('dlink'):
            return {**track_info, "dlink": download_data.get('dlink')}
        else:
            return {**track_info, "error": "Download link not available"}


def main():
    track_id = "7so0lgd0zP2Sbgs2d7a1SZ"
    spotify_url = f"https://open.spotify.com/track/{track_id}"
    
    spotify = SpotiSongDownloader()
    result = spotify.get_download_info(spotify_url)
    
    spotify.logger.info('Test track: ' + json.dumps(result, indent=2))


if __name__ == "__main__":
    main()