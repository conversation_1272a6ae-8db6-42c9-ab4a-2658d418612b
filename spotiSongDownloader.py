import json
import random
import requests
import urllib.parse
import re
import logging
import threading
from datetime import datetime, timedelta
from typing import Dict, Any, Set
from notifier import notify_owner_sync, notify_user_sync
from getTracks import SpotiSongDownloader

# https://github.com/afkarxyz/SpotiSongDownloader
# https://spotisongdownloader.to/

logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

# Configurable settings
quality = "m4a"  # Качество песни (m4a, 320, 256, 192, 128, 64)
# Default music download service URL
# mymp3.xyz - 104.243.45.220 | hf27e392f8430c81cac.free.beeceptor.com
# awd1.mymp3.xyz - 74.50.96.114 | whf27e392f8430c81cac.free.beeceptor.com
# awd2.mymp3.xyz - 74.50.126.115 | whf27e392f8430c81cac.free.beeceptor.com
# awd3.mymp3.xyz - 107.155.87.192 | whf27e392f8430c81cac.free.beeceptor.com
# awd4.mymp3.xyz - 74.50.126.120 | whf27e392f8430c81cac.free.beeceptor.com
# awd13.mymp3.xyz - 66.165.243.218 | whf27e392f8430c81cac.free.beeceptor.com
MUSIC_DOWNLOAD_SERVICE_URL = "https://awd1.mymp3.xyz"

# Function to update the music download service URL
def update_music_service_url(new_url):
    """Update the music download service URL."""
    global MUSIC_DOWNLOAD_SERVICE_URL
    old_url = MUSIC_DOWNLOAD_SERVICE_URL
    MUSIC_DOWNLOAD_SERVICE_URL = new_url
    logger.info(f"[SpotiSongDownloader] Music download service URL updated from {old_url} to {new_url}")
    return old_url

# Кэш в памяти
class TrackCache:
    def __init__(self, expiry_days=1):
        self.tracks: Dict[str, str] = {}  # {track_id: download_link}
        self.last_updated = datetime.now()
        self.expiry_days = expiry_days
        self.lock = threading.RLock()  # Для потокобезопасности
        self.updating_tracks: Set[str] = set()  # Множество треков, которые сейчас обновляются

    def get(self, track_id: str) -> str:
        """Получает ссылку из кэша"""
        with self.lock:
            # Проверяем срок действия кэша
            if datetime.now() - self.last_updated >= timedelta(days=self.expiry_days):
                logger.info("[SpotiSongDownloader] Cache expired, clearing...")
                self.tracks = {}
                self.last_updated = datetime.now()
                return None

            return self.tracks.get(track_id)

    def set(self, track_id: str, download_link: str) -> None:
        """Добавляет или обновляет ссылку в кэше"""
        with self.lock:
            self.tracks[track_id] = download_link
            # Обновляем timestamp только когда добавляем новый элемент
            if len(self.tracks) == 1:
                self.last_updated = datetime.now()

            # Удаляем трек из списка обновляемых
            if track_id in self.updating_tracks:
                self.updating_tracks.remove(track_id)

    def is_updating(self, track_id: str) -> bool:
        """Проверяет, обновляется ли сейчас трек"""
        with self.lock:
            return track_id in self.updating_tracks

    def mark_as_updating(self, track_id: str) -> None:
        """Помечает трек как обновляемый"""
        with self.lock:
            self.updating_tracks.add(track_id)

    def unmark_as_updating(self, track_id: str) -> None:
        """Снимает отметку об обновлении трека"""
        with self.lock:
            if track_id in self.updating_tracks:
                self.updating_tracks.remove(track_id)

# Глобальный объект кэша
track_cache = TrackCache()

# Новый скачиватель треков
downloader = SpotiSongDownloader()

def get_cookie():
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'Origin': 'https://spotisongdownloader.to',
        'Referer': 'https://spotisongdownloader.to/'
    }
    try:
        logger.info("[SpotiSongDownloader.get_cookie] Getting PHPSESSID cookie from spotisongdownloader.to")
        session = requests.Session()
        response = session.get('https://spotisongdownloader.to/', headers=headers)
        response.raise_for_status()
        cookies = session.cookies.get_dict()
        logger.info(f"[SpotiSongDownloader.get_cookie] Retrieved cookies: {cookies}")

        # logger.info("[SpotiSongDownloader.get_cookie] Getting cf_token from spotisongdownloader.to")
        # response = session.get('https://spotisongdownloader.to/createToken.php', headers=headers, cookies=cookies)
        # response.raise_for_status()
        # Придёт `document.cookie = 'cf_token=150e40ab7c322feecb7f4ce8093e9a95; path=/; SameSite=Strict;';`
        # match = re.search(r"cf_token=([^;]+)", response.text)
        # if match:
        #     res = f"PHPSESSID={cookies['PHPSESSID']}; cf_token={match.group(1)}; ttpassed=ttpassed; quality={quality}"
        #     logger.info(f"[SpotiSongDownloader.get_cookie] Result cookie: {res}")
        #     return res
        # logger.warning("[SpotiSongDownloader.get_cookie] cf_token pattern not found in response")

        res = f"PHPSESSID={cookies['PHPSESSID']}; quality={quality}"
        logger.info(f"[SpotiSongDownloader.get_cookie] Result cookie: {res}")
        return res
    except requests.exceptions.RequestException as e:
        logger.error(f"[SpotiSongDownloader.get_cookie] Failed to get cookie: {str(e)}")
        return None
    except Exception as e:
        logger.error(f"[SpotiSongDownloader.get_cookie] Unexpected error: {str(e)}")
        return None

def get_cf_token(cookie):
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/133.0.0.0 Safari/537.36',
        'Cookie': cookie,
        'Origin': 'https://spotisongdownloader.to',
        'Referer': 'https://spotisongdownloader.to/track.php',
    }
    try:
        logger.info("[SpotiSongDownloader.get_cf_token] Getting cf_token from spotisongdownloader.to")
        response = requests.get('https://spotisongdownloader.to/createToken.php', headers=headers)
        response.raise_for_status()

        match = re.search(r"cf_token=([^;]+)", response.text)
        if match:
            cf_token = match.group(1)
            logger.info(f"[SpotiSongDownloader.get_cf_token] Found cf_token: {cf_token}")
            return cf_token
        logger.warning("[SpotiSongDownloader.get_cf_token] cf_token pattern not found in response")
        return None
    except requests.exceptions.RequestException as e:
        logger.error(f"[SpotiSongDownloader.get_cf_token] Failed to get cf_token: {str(e)}")
        return None
    except Exception as e:
        logger.error(f"[SpotiSongDownloader.get_cf_token] Unexpected error: {str(e)}")
        return None


def get_api(cookie):
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/133.0.0.0 Safari/537.36',
        'Cookie': cookie,
        'Origin': 'https://spotisongdownloader.to',
        'Referer': 'https://spotisongdownloader.to/',
    }
    try:
        logger.info("[SpotiSongDownloader.get_api] Getting API endpoint from spotisongdownloader.to")
        response = requests.get('https://spotisongdownloader.to/track.php', headers=headers)
        response.raise_for_status()

        match = re.search(r'url:\s*"(/api/composer/spotify/[^"]+)"', response.text)
        if match:
            api_endpoint = match.group(1)
            logger.info(f"[SpotiSongDownloader.get_api] Found API endpoint: {api_endpoint}")
            return f"https://spotisongdownloader.to{api_endpoint}", response.cookies
        logger.warning("[SpotiSongDownloader.get_api] API endpoint pattern not found in response")

    except requests.exceptions.RequestException as e:
        logger.error(f"[SpotiSongDownloader.get_api] Failed to get API endpoint: {str(e)}")
        return None, None
    except Exception as e:
        logger.error(f"[SpotiSongDownloader.get_api] Unexpected error: {str(e)}")
        return None, None

# Raw from mymp3.xyz
def get_link_mymp3(track_id, track_data):
    provider_url = f"{MUSIC_DOWNLOAD_SERVICE_URL}/svd/"
    spotify_url = f"https://open.spotify.com/track/{track_id}"
    try:
        logger.info(f"[SpotiSongDownloader.get_link_mymp3] Getting download link from {MUSIC_DOWNLOAD_SERVICE_URL} for: {spotify_url}")

        track_data = track_data if track_data else None
        if not track_data:
            track_data_res = downloader.get_track_info(spotify_url)
            track_data = {
                'song_name': track_data_res.get('song_name', ''),
                'artist': track_data_res.get('artist', '')
            }

        logger.info(f"[SpotiSongDownloader.get_link_mymp3] Track data: {track_data}")
        if not track_data['song_name'] and not track_data['artist']:
            logger.error(f"[SpotiSongDownloader.get_link_mymp3] Invalid track data: {track_data}")
            return None, "Invalid track data"

        params = {
            'url': spotify_url,
            'song_name': urllib.parse.quote(track_data['song_name'], safe=':/?='),
            'artist_name': urllib.parse.quote(track_data['artist'], safe=':/?='),
            # between c1.txt to c10.txt
            'cookie_file': f"c{random.randint(1, 10)}.txt"
            }
        logger.info(f"[SpotiSongDownloader.get_link_mymp3] Sending request to {provider_url} with params: {params}")
        response = requests.get(provider_url, params=params)
        response.raise_for_status()
        result_link = response.json()
        logger.info(f"[SpotiSongDownloader.get_link_mymp3] Download link retrieved: {result_link}")
        
        if not result_link.get('dlink'):
            logger.error(f"[SpotiSongDownloader.get_link_mymp3] Download link not found in response: {result_link}")
            return None, "Download link not found"

        return result_link.get('dlink'), None
    except json.JSONDecodeError as json_err:
        error_msg = str(json_err)
        logger.error(f"[SpotiSongDownloader.get_link_mymp3] JSON decode error: {error_msg}")
        return None, error_msg
    except requests.exceptions.RequestException as e:
        error_msg = str(e)
        logger.error(f"[SpotiSongDownloader.get_link_mymp3] Failed to get download link from {MUSIC_DOWNLOAD_SERVICE_URL}: {error_msg}")
        return None, error_msg
    except Exception as e:
        error_msg = str(e)
        logger.error(f"[SpotiSongDownloader.get_link_mymp3] Unexpected error: {error_msg}")
        return None, error_msg
    
# From mymp3.xyz but from apple
def get_link_mymp3_apple(track_id, track_data):
    search_url = f"https://api.song.link/v1-alpha.1/links/"
    provider_url = f"{MUSIC_DOWNLOAD_SERVICE_URL}/awd/"
    try:
        logger.info(f"[SpotiSongDownloader.get_link_mymp3_apple] Search apple music from {MUSIC_DOWNLOAD_SERVICE_URL} for: {track_id}")

        track_data = track_data if track_data else None
        if not track_data:
            track_data_res = downloader.get_track_info(f"https://open.spotify.com/track/{track_id}")
            track_data = {
                'song_name': track_data_res.get('song_name', ''),
                'artist': track_data_res.get('artist', ''),
                'album': track_data_res.get('album', '')
            }

        logger.info(f"[SpotiSongDownloader.get_link_mymp3_apple] Track data: {track_data}")
        if not track_data['song_name'] and not track_data['artist']:
            logger.error(f"[SpotiSongDownloader.get_link_mymp3_apple] Invalid track data: {track_data}")
            return None, "Invalid track data"

        params = {
            'songIfSingle': 'true',
            'platform': 'spotify',
            'type': 'song',
            'id': track_id
            }
        logger.info(f"[SpotiSongDownloader.get_link_mymp3_apple] Sending request to {search_url} with params: {params}")
        response = requests.get(search_url, params=params)
        response.raise_for_status()

        search_result = response.json()
        apple_music_url = search_result.get('linksByPlatform', {}).get('appleMusic', {}).get('url')
        if not apple_music_url:
            logger.error(f"[SpotiSongDownloader.get_link_mymp3_apple] Apple music url not found in response: {search_result}")
            return None, "Apple music url not found"
        
        logger.info(f"[SpotiSongDownloader.get_link_mymp3_apple] Apple music url found: {apple_music_url}")
        params = {
            'url': apple_music_url,
            'song_name': urllib.parse.quote(track_data['song_name'], safe=':/?='),
            'artist_name': urllib.parse.quote(track_data['artist'], safe=':/?=')
            }
        logger.info(f"[SpotiSongDownloader.get_link_mymp3_apple] Sending request to {provider_url} with params: {params}")
        response = requests.get(provider_url, params=params)
        response.raise_for_status()
        result_link_result = response.json()
        result_link = result_link_result.get('dlink')
        if not result_link:
            logger.error(f"[SpotiSongDownloader.get_link_mymp3_apple] Download link not found in response: {result_link_result}")
            return None, "Download link not found"
        
        if "/phmp4" in result_link:
            result_link = result_link.replace("/phmp4", "/phmp3")
        
        return result_link, None
    except json.JSONDecodeError as json_err:
        error_msg = str(json_err)
        logger.error(f"[SpotiSongDownloader.get_link_mymp3_apple] JSON decode error: {error_msg}")
        return None, error_msg
    except requests.exceptions.RequestException as e:
        error_msg = str(e)
        logger.error(f"[SpotiSongDownloader.get_link_mymp3_apple] Failed to get download link from {MUSIC_DOWNLOAD_SERVICE_URL}: {error_msg}")
        return None, error_msg
    except Exception as e:
        error_msg = str(e)
        logger.error(f"[SpotiSongDownloader.get_link_mymp3_apple] Unexpected error: {error_msg}")
        return None, error_msg

# Primary
def get_track_data(track_id):
    spotify_url = f"https://open.spotify.com/track/{track_id}"
    cookie = get_cookie()
    if not cookie:
        return None, None, "Failed to get cookie"

    try:
        logger.info(f"[SpotiSongDownloader] Getting track data for track ID: {track_id}")
        response = requests.get(
            'https://spotisongdownloader.to/api/composer/spotify/xsingle_track.php',
            params={'url': spotify_url},
            headers={'Cookie': cookie}
        )
        response.raise_for_status()
        track_data = response.json()
        metadata = {
            'song_name': track_data.get('song_name', ''),
            'artist': track_data.get('artist', ''),
            'img': track_data.get('img', ''),
            'released': track_data.get('released', ''),
            'album_name': track_data.get('album_name', '')
        }
        logger.info(f"[SpotiSongDownloader] Track data retrieved: '{track_data}'")
        return track_data, metadata, cookie
    except requests.exceptions.RequestException as e:
        error_msg = str(e)
        logger.error(f"[SpotiSongDownloader] Failed to get track data: {error_msg}")
        return None, None, f"Failed to retrieve track data: {error_msg}"

# Fallback
def search_track(track_data, cookie):
    ytsearch_url = "https://spotisongdownloader.to/api/composer/ytsearch/mytsearch.php"
    params = {
        'name': track_data['song_name'],
        'artist': track_data['artist'],
        'album': track_data['album_name'],
        'link': track_data['url']
    }
    headers = {'Cookie': cookie}
    try:
        logger.info(f"[SpotiSongDownloader] Searching for YouTube video for track ID: {track_data['url']}")
        response = requests.get(ytsearch_url, params=params, headers=headers)
        response.raise_for_status()
        return response.json(), None
    except requests.exceptions.RequestException as e:
        error_msg = str(e)
        logger.error(f"[SpotiSongDownloader] Failed to search YouTube video: {error_msg}")
        return None, error_msg

# Primary
def get_url(track_data, cookie, track_id):
    url, new_cookie = get_api(cookie)
    if not url:
        return None

    if new_cookie:
        cookie = new_cookie

    payload = {
        'song_name': track_data['song_name'],
        'artist_name': track_data['artist'],
        'url': track_data['url']
    }

    headers = {
        # 'Accept': 'application/json, text/javascript, */*; q=0.01',
        'Cookie': cookie,
        'Origin': 'https://spotisongdownloader.to',
        'Referer': 'https://spotisongdownloader.to/track.php',
        'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
    }

    try:
        logger.info(f"[SpotiSongDownloader.get_url] Getting download link for track ID: {track_id}. Url: {url}\nHeades: {headers}\nPayload: {payload}")
        new_cf_token = get_cf_token(cookie)
        if new_cf_token:
            headers['Cookie'] = re.sub(r'cf_token=[^;]+', f'cf_token={new_cf_token}', headers['Cookie'])
        response = requests.post(url, data=payload, headers=headers)
        response.raise_for_status()

        try:
            download_data = response.json()
        except json.JSONDecodeError as json_err:
            logger.error(f"[SpotiSongDownloader.get_url] JSON decode error: {str(json_err)}, StatusCode: {response.status_code}, Response: {response.text}")
            notify_owner_sync(f"❗⌚JSON decode error for track ID: {track_id}. Use fallback\nStatusCode: {response.status_code}\nResponse: '{response.text}'\nError: {str(json_err)}", True)
            return None

        logger.info(f"[SpotiSongDownloader.get_url] Download link retrieved: {download_data}")

        encoded_link = urllib.parse.quote(download_data['dlink'], safe=':/?=')

        # Почему-то при генерации ссылки ниже сервис отдаёт некорректные headers для аудио-файла. Так ещё он и весит больше. Бред
        # Пока что отдём dlink
        return encoded_link
        payload = {
            'url': encoded_link,
            'name': track_data['song_name'],
            'artist': track_data['artist'],
            'album': track_data['album_name'],
            'thumb': track_data['img'],
            'released': track_data['released']
        }
        logger.info(f"[SpotiSongDownloader] Sending request to save ID3\nPayload: {payload}")
        responseSaveId3 = requests.post("https://spotisongdownloader.to/api/composer/ffmpeg/saveid3.php", data=payload, headers=headers)
        if responseSaveId3.status_code != 200:
            logger.error(f"[SpotiSongDownloader] Failed to save ID3: {responseSaveId3.text}")
            notify_owner_sync(f"❗Failed get true url. Use dlink.\nTitle: {track_data['artist']} - {track_data['song_name']}\nTrack ID: {track_id}", True)
            return encoded_link

        logger.info(f"[SpotiSongDownloader] Successfully saved ID3. Result: {responseSaveId3.text}")
        return "https://spotisongdownloader.to/api/composer/ffmpeg/saved/" + urllib.parse.quote(responseSaveId3.text, safe=':/?=')
    except Exception as e:
        error_msg = str(e)
        logger.error(f"[SpotiSongDownloader] Failed to get URL: {error_msg}")
        return None

# Fallback
def get_link(track_data, yt_data, cookie):
    rapidmp3_url = "https://spotisongdownloader.to/api/rapidmp3.php"
    params = {
        'q': yt_data['videoid'],
        'url': track_data['url'],
        'name': track_data['song_name'],
        'artist': track_data['artist'],
        'album': track_data['album_name']
    }
    headers = {'Cookie': cookie}
    try:
        logger.info(f"[SpotiSongDownloader] Getting download link for YouTube video ID: {yt_data['videoid']}")
        response = requests.get(rapidmp3_url, params=params, headers=headers)
        response.raise_for_status()
        return response.json(), None
    except requests.exceptions.HTTPError as e:
        status_code = e.response.status_code
        try:
            body = e.response.text
        except:
            body = "Unable to get response body"
        error_msg = f"HTTP Error {status_code}: {str(e)}\nResponse body: {body}"
        logger.error(f"[SpotiSongDownloader] HttpError. Failed to get download link: {error_msg}")
        return None, error_msg
    except Exception as e:
        error_msg = str(e)
        logger.error(f"[SpotiSongDownloader] Failed to get download link: {error_msg}")
        return None, error_msg

def convert_link(download_data, cookie):
    convert_url = "https://spotisongdownloader.to/api/convertRapidAPI.php"
    params = {'url': download_data['link']}
    headers = {'Cookie': cookie}
    try:
        logger.info(f"[SpotiSongDownloader] Converting link: {download_data['link']}")
        response = requests.post(convert_url, data=params, headers=headers)
        response.raise_for_status()
        return response.json(), None
    except Exception as e:
        error_msg = str(e)
        logger.error(f"[SpotiSongDownloader] Failed to convert link: {error_msg}")
        return None, error_msg

def update_cache_in_background(track_id, user_id=None, track_metadata=None):
    """Background thread to update the cache for a track ID"""
    try:
        track_data = None
        if track_metadata:
            logger.info(f"[SpotiSongDownloader] Using provided track metadata: {track_metadata}")
            track_data = track_metadata

        download_link, error_msg = get_link_mymp3(track_id, track_data)

        if not download_link:
            logger.error(f"[SpotiSongDownloader] Failed to get download link from {MUSIC_DOWNLOAD_SERVICE_URL} for track ID: {track_id}")
            notify_owner_sync(f"❗Failed to get download link from {MUSIC_DOWNLOAD_SERVICE_URL}. Use fallback.\nTrack ID: {track_id}\nError: {error_msg}", True)
            download_link, error_msg = get_link_mymp3_apple(track_id, track_data)

        if not download_link:
            spotify_url = f"https://open.spotify.com/track/{track_id}"
            data = downloader.get_download_info(spotify_url)
            if "error" in data:
                logger.error(f"[SpotiSongDownloader] Error from API: {data['error']}")
                notify_owner_sync(f"❗⌚️Error from API for track ID: {track_id}\nError: {data['error']}", True)

            download_link = data.get('dlink', None)

        # if not download_link:
        #     logger.info(f"[SpotiSongDownloader] Primary download link not found, using fallback for track ID: {track_id}")

        #     track_data, metadata, cookie_or_error = get_track_data(track_id)
        #     if not track_data:
        #         logger.error(f"[SpotiSongDownloader] Failed to get track data for track ID: {track_id}")
        #         track_cache.unmark_as_updating(track_id)
        #         notify_owner_sync(f"❗Failed to get track data.\nTrack ID: {track_id}\nError: {cookie_or_error}")
        #         if user_id:
        #             notify_user_sync(user_id, f"❗ Failed to get track data. Please try again later.")
        #         return

        #     yt_data, error_msg = search_track(track_data, cookie_or_error)
        #     if not yt_data:
        #         logger.error(f"[SpotiSongDownloader] Failed to search YouTube data for track ID: {track_id}")
        #         track_cache.unmark_as_updating(track_id)
        #         notify_owner_sync(f"❗Failed to get YouTube data for track.\nTitle: {track_data['artist']} - {track_data['song_name']}\nTrack ID: {track_id}\nError: {error_msg}")
        #         if user_id:
        #             notify_user_sync(user_id, f"❗ Failed to find track. Please try again later. {track_data['artist']} - {track_data['song_name']}")
        #         return

        #     download_data, error_msg = get_link(track_data, yt_data, cookie_or_error)
        #     if not download_data:
        #         logger.error(f"[SpotiSongDownloader] Failed to get download link for track ID: {track_id}")
        #         track_cache.unmark_as_updating(track_id)
        #         notify_owner_sync(f"❗Failed to get download link for track.\nTitle: {track_data['artist']} - {track_data['song_name']}\nTrack ID: {track_id}\nError: {error_msg}")
        #         return

        #     converted_data, error_msg = convert_link(download_data, cookie_or_error)
        #     if not converted_data:
        #         logger.error(f"[SpotiSongDownloader] Failed to get download URL for track ID: {track_id}")
        #         track_cache.unmark_as_updating(track_id)
        #         notify_owner_sync(f"❗Failed to get download URL for track.\nTitle: {track_data['artist']} - {track_data['song_name']}\nTrack ID: {track_id}\nError: {error_msg}")
        #         return

        #     download_link = converted_data['dlink']

        if download_link:
            download_link = urllib.parse.quote(download_link, safe=':/?=')
            track_cache.set(track_id, download_link)
            logger.info(f"[SpotiSongDownloader] Successfully updated cache with download link for track ID: {track_id}. Link: '{download_link}'")
            notify_owner_sync(f"Title: {track_data['artist']} - {track_data['song_name']}\nTrack ID: {track_id}\nDownload Link: {download_link}", True)
            if user_id:
                notify_user_sync(user_id, f"✅ Track ready for download: {track_data['artist']} - {track_data['song_name']}")
        else:
            track_cache.unmark_as_updating(track_id)
            if user_id:
                notify_user_sync(user_id, f"❗ Failed to get download link for track {track_data['artist']} - {track_data['song_name']}")
    except Exception as e:
        error_msg = str(e)
        logger.error(f"[SpotiSongDownloader] Error updating cache for track ID {track_id}: {error_msg}")
        track_cache.unmark_as_updating(track_id)
        notify_owner_sync(f"❗Unexpected error while processing track ID: {track_id}\nError: {error_msg}")
        if user_id:
            notify_user_sync(user_id, f"❗ An error occurred while processing the track. Please try again later.")

def download_track(track_id, user_id=None, track_metadata=None):
    """Get audio download link for a Spotify track ID"""
    logger.info(f"[SpotiSongDownloader] Processing download request for track ID: {track_id}, User ID: {user_id}")

    # Проверяем наличие трека в кэше
    cached_link = track_cache.get(track_id)
    if cached_link:
        return cached_link

    # Если нет в кэше, проверяем не обновляется ли он уже
    if not track_cache.is_updating(track_id):
        # Помечаем трек как обновляемый
        track_cache.mark_as_updating(track_id)

        # Запускаем фоновое обновление кэша
        threading.Thread(
            target=update_cache_in_background,
            args=(track_id, user_id, track_metadata),
            daemon=True
        ).start()

        logger.info(f"[SpotiSongDownloader] Track ID {track_id} not found in cache. Background update started.")
    else:
        logger.info(f"[SpotiSongDownloader] Track ID {track_id} is already being updated.")

    return None
