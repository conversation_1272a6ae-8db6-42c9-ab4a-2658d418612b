version: '3.8'

services:
  nginx:
    image: nginx:latest
    ports:
      - "8443:8443"  # Маппинг хост порта 8443 на контейнерный порт 8443
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/certs:/etc/nginx/certs:ro
    depends_on:
      - now-play-bot
    restart: on-failure

  now-play-bot:
    image: ghcr.io/sijykijy/tg-now-play-bot/now-play-bot:main
    #ports:
    #  - "8443:8443"
    environment:
      TELEGRAM_TOKEN: ${TELEGRAM_TOKEN}
      SPOTIFY_CLIENT_ID: ${SPOTIFY_CLIENT_ID}
      SPOTIFY_CLIENT_SECRET: ${SPOTIFY_CLIENT_SECRET}
      SPOTIFY_REDIRECT_URI: ${SPOTIFY_REDIRECT_URI}
      APP_URL: ${APP_URL}
      PORT: 88
      TELEGRAM_OWNER_ID: 123
    expose:
      - "88"  # Экспортирует порт для внутреннего использования Docker-сети
    restart: on-failure