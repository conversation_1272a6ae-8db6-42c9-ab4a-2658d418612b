name: docker-build

on:
  push:
    branches: [ main ]
    paths:
      - '**/*.py'  # Запуск при изменении любых .py файлов

  workflow_dispatch:  # Позволяет запускать workflow вручную

env:
  IMAGE_NAME: now-play-bot
  REGISTRY: ghcr.io

jobs:

  # test:
  #   runs-on: ubuntu-latest
  #   steps:
  #     - uses: actions/checkout@v3

  #     - name: Build test docker image
  #       run: docker build -t $IMAGE_NAME .

  #     - name: Run container
  #       run: docker run --name test-container -d -e CODE='test' $IMAGE_NAME
          
  #     - name: Checking service
  #       run: |
  #         # Ждем 5 секунд, чтобы сервис успел запуститься
  #         sleep 5
          
  #         # Проверяем, что контейнер запущен
  #         if [ "$(docker inspect -f '{{.State.Running}}' test-container)" != "true" ]; then
  #           echo "Контейнер не запущен"
  #           exit 1
  #         fi
          
  #         # Здесь нужно добавить проверки, специфичные для вашего сервиса
  #         # Например, проверка доступности API endpoint:
  #         # curl -f http://localhost:8080/head || exit 1
          
  #         echo "Сервис успешно запущен и прошел базовые проверки"
      
  #     - name: Логи контейнера при ошибке
  #       if: failure()
  #       run: docker logs test-container
          
  #     - name: Остановка и удаление тестового контейнера
  #       if: always()
  #       run: |
  #         docker stop test-container
  #         docker rm test-container

  build:
    runs-on: ubuntu-latest
    # needs: test

    steps:
      - uses: actions/checkout@v2
      - name: Build and push image
        run: |
          REPO_NAME=${GITHUB_REPOSITORY#*/}
          GITHUB_NAME=${GITHUB_ACTOR,,}       
          DOCKER_IMAGE=${REGISTRY}/${GITHUB_NAME}/${REPO_NAME,,}/${IMAGE_NAME}:${GITHUB_REF##*/}
          
          echo ${{ secrets.GITHUB_TOKEN }} | docker login ${REGISTRY} -u ${GITHUB_NAME} --password-stdin
          docker build -f Dockerfile --pull -t $DOCKER_IMAGE .
          docker push $DOCKER_IMAGE

  # deploy:
  #   runs-on: ubuntu-latest
  #   needs: build
  #   steps:
  #     - uses: satak/webrequest-action@master
  #       name: Post request for update
  #       with:
  #         url: ${{ secrets.CALLBACK_URI }}
  #         method: POST