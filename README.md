# Spotify Recent Tracks Telegram Bot

## Description

This exemplary Telegram bot, meticulously crafted in Python, enables users to effortlessly browse and share their recently played tracks from the renowned Spotify platform. Operating seamlessly in inline mode, this bot ensures a sophisticated interaction experience right within Telegram chats.

## Key Features

- Efficiently retrieves a rich history of recently played tracks using the Spotify API
- Presents a carefully curated list of tracks for user selection through Telegram's intuitive inline interface
- Provides an MP3 file of the user-selected track, enhancing the listening experience
- Generates a universal link to the chosen track via the respected song.link service

## Technologies

- Python
- Telegram Bot API (inline mode)
- Spotify API
- song.link API

## How It Works

1. User initiates interaction with the bot in any Telegram chat environment
2. <PERSON><PERSON> skillfully retrieves a comprehensive list of recently played tracks from Spotify
3. Tracks are elegantly displayed as inline results for swift navigation
4. Upon track selection, the bot promptly delivers an MP3 file along with the corresponding song.link URL

## Links

- [Telegram Bot API](https://core.telegram.org/bots/api)
- [Spotify API](https://developer.spotify.com/documentation/web-api/)
- [song.link](https://song.link/)

## Installation

```bash
git clone https://github.com/SijyKijy/tg-now-play-bot.git
cd tg-now-play-bot
pip install -r requirements.txt
```

## Configuration

1. Create a `.env` file in the project root
2. Add the following environment variables:

```yml
TELEGRAM_TOKEN=''
SPOTIFY_CLIENT_ID=''
SPOTIFY_CLIENT_SECRET=''
WEB_SERVER_HOST=''
PORT=''
SPOTIFY_REDIRECT_URI=''
APP_URL=''
TELEGRAM_OWNER_ID=''
```

## Usage

Run the bot using the following command:

```bash
python main.py
```

# Update / generate dependencies

pipreqs .
