import logging
import asyncio
from typing import Callable, Optional

logger = logging.getLogger(__name__)

# Будет установлено из main.py
notify_owner_function: Optional[Callable[[str, bool], None]] = None
notify_user_function: Optional[Callable[[int, str], None]] = None
main_event_loop: Optional[asyncio.AbstractEventLoop] = None

def set_notify_owner_function(func: Callable[[str, bool], None], loop: asyncio.AbstractEventLoop):
    """Установить функцию для уведомления владельца"""
    global notify_owner_function, main_event_loop
    notify_owner_function = func
    main_event_loop = loop
    logger.info("Функция NotifyOwner была установлена")

def set_notify_user_function(func: Callable[[int, str], None]):
    """Установить функцию для уведомления пользователя по ID"""
    global notify_user_function
    notify_user_function = func
    logger.info("Функция NotifyUser была установлена")

async def notify_owner(message: str, disable_notification: bool = False):
    """Отправить сообщение владельцу бота"""
    if notify_owner_function:
        await notify_owner_function(message, disable_notification)
    else:
        logger.warning("Function NotifyOwner not set. Message not sent.")

def notify_owner_sync(message: str, disable_notification: bool = False):
    """Отправить сообщение владельцу бота (синхронная обертка)"""
    if notify_owner_function and main_event_loop:
        asyncio.run_coroutine_threadsafe(
            notify_owner_function(message, disable_notification),
            main_event_loop
        )
    else:
        logger.warning("NotifyOwner function or event loop not set. Message not sent.")

def notify_user_sync(user_id: int, message: str):
    """Отправить сообщение пользователю по ID (синхронная обертка)"""
    if notify_user_function and main_event_loop:
        asyncio.run_coroutine_threadsafe(
            notify_user_function(user_id, message),
            main_event_loop
        )
    else:
        logger.warning(f"NotifyUser function or event loop not set. Message to user {user_id} not sent.")
